<template>
  <div class="q-ml-md">
    <!-- รายการตัวเลือก (Radio Input หน้า Text Field) -->
    <div
      v-for="(option, index) in store.radioOptions"
      :key="index"
      class="row items-center q-mb-sm draggable-row"
      @dragover.prevent
      @drop="store.drop(index, $event)"
      :style="{ transition: 'all 0.3s ease', opacity: store.draggedIndex === index ? 0.5 : 1 }"
    >
      <q-btn
        flat
        round
        icon="drag_indicator"
        color="grey"
        @mousedown="store.startDrag(index)"
        draggable="true"
        @dragstart="store.handleDragStart($event)"
        @dragend="store.endDrag"
        @mouseover="store.hoverRow(index)"
        style="cursor: move"
      />
      <q-radio
        v-model="store.checkboxOptions"
        :val="store.radioOptions[index]?.value"
        color="primary"
        disable
        class="q-mr-sm"
      />
      <div class="row items-center">
        <q-input
          v-model="store.radioOptions[index]!.optionText"
          :placeholder="store.radioOptions[index]!.placeholder"
          dense
          @input="handleOptionTextChange(index, $event)"
          @blur="handleOptionTextBlur(index)"
          class="q-mr-sm"
        />
        <q-input
          v-model.number="store.radioOptions[index]!.score"
          placeholder="คะแนน"
          type="number"
          dense
          style="max-width: 80px"
          @input="handleOptionScoreChange(index, $event)"
        />
      </div>
      <q-btn flat round icon="image" color="grey" class="q-ml-sm" />
      <q-btn
        v-if="store.radioOptions.length > 1"
        flat
        round
        icon="close"
        @click="store.removeOption(index)"
        :disable="store.radioOptions.length <= 1"
        class="q-ml-sm"
      />
    </div>

    <!-- ปุ่มเพิ่มช้อย -->
    <div class="row items-center q-mt-sm">
      <q-btn
        flat
        color="secondary"
        label="เพิ่มตัวเลือก"
        icon="add"
        @click="handleAddOption()"
        :loading="isCreatingOption"
        :disable="isCreatingOption"
      />
      <q-btn
        flat
        color="secondary"
        label="เพิ่ม 'อื่นๆ'"
        icon="add"
        @click="handleAddOtherOption()"
        :loading="isCreatingOption"
        :disable="isCreatingOption"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { inject, ref } from 'vue';
import type { ItemBlockStore } from 'src/stores/item_block_store';
import type { ItemBlock, Option } from 'src/types/models';
import { OptionService, type CreateOptionData } from 'src/services/asm/optionService';
import { Notify } from 'quasar';

// Define interface for option update data
interface OptionUpdateData {
  index: number;
  option: Option;
}

// Inject the store from the parent component
const store = inject<ItemBlockStore>('blockStore');

// Inject auto-save functions from parent ItemBlockComponent
const autoSave = inject<{
  triggerOptionAutoSave: (
    optionId: number,
    field: 'optionText' | 'value',
    value: string | number,
  ) => void;
  isSaving: { value: boolean };
}>('autoSave');

// Make sure the store is available
if (!store) {
  console.error('ItemBlockStore not provided to ChoiceAns component');
  throw new Error('ItemBlockStore not provided to ChoiceAns component');
}

// Get the itemBlock prop to access actual option IDs
const props = defineProps<{
  itemBlock: ItemBlock;
}>();

// Define emits for updating parent component
const emit = defineEmits<{
  'update:options': [options: Option[]];
  'option:created': [option: Option];
  'option:updated': [optionId: number, data: OptionUpdateData];
}>();

// Initialize option service
const optionService = new OptionService();

// Loading state for option creation
const isCreatingOption = ref(false);

// Handler for option text changes with debounced auto-save
const handleOptionTextChange = (index: number, event: Event) => {
  const target = event.target as HTMLInputElement;
  const newValue = target.value;

  // Update the store immediately for UI responsiveness
  store.updateOption(index);

  // Get the actual option ID from the itemBlock prop
  const actualOption = props.itemBlock.options?.[index];

  // Debug logging to understand the state
  console.log('🔍 Option text change debug:', {
    index,
    newValue,
    actualOption: actualOption
      ? { id: actualOption.id, optionText: actualOption.optionText }
      : null,
    hasAutoSave: !!autoSave,
    storeOption: store.radioOptions[index],
  });

  // Only trigger auto-save if the option has an ID (exists in backend)
  if (actualOption && actualOption.id && autoSave) {
    console.log('✅ Triggering auto-save for option:', actualOption.id, 'with text:', newValue);
    // Use the debounced auto-save from parent component
    autoSave.triggerOptionAutoSave(actualOption.id, 'optionText', newValue);
  } else {
    console.log('❌ Auto-save not triggered. Reasons:', {
      hasActualOption: !!actualOption,
      hasOptionId: actualOption?.id,
      hasAutoSave: !!autoSave,
    });
  }
};

// Handler for option text blur (immediate save for important changes)
const handleOptionTextBlur = async (index: number) => {
  const actualOption = props.itemBlock.options?.[index];

  // If option doesn't have an ID yet, it needs to be created first
  if (actualOption && !actualOption.id) {
    const optionText = store.radioOptions[index]?.optionText || '';

    // Only create if user has entered some text
    if (optionText.trim()) {
      try {
        const optionData: CreateOptionData = {
          optionText: optionText,
          itemBlockId: props.itemBlock.id,
          value: store.radioOptions[index]?.score || 0,
        };

        const createdOption = await optionService.createOption(optionData);

        // Emit option update to parent component
        emit('option:updated', createdOption.id, {
          index,
          option: createdOption,
        });

        console.log('✅ Option created on blur:', createdOption);
      } catch (error) {
        console.error('❌ Failed to create option on blur:', error);
      }
    }
  }
};

// Handler for option score changes with auto-save
const handleOptionScoreChange = async (index: number, event: Event) => {
  const target = event.target as HTMLInputElement;
  const newValue = Number(target.value) || 0;

  // Update the store
  store.updateOption(index);

  // Trigger auto-save if we have the option ID and auto-save is available
  // Get the actual option ID from the itemBlock prop
  const actualOption = props.itemBlock.options?.[index];
  if (actualOption && autoSave && actualOption.id) {
    // For score changes, we need to update the value field
    autoSave.triggerOptionAutoSave(actualOption.id, 'value', newValue);
  } else if (actualOption && actualOption.id) {
    // Fallback: use optionService directly if auto-save is not available
    try {
      await optionService.updateOption(actualOption.id, {
        optionText: actualOption.optionText,
        itemBlockId: props.itemBlock.id,
        value: newValue,
      });
    } catch (error) {
      console.error('Failed to update option score:', error);
    }
  }
};

// Handler for adding new option
const handleAddOption = async () => {
  if (isCreatingOption.value) return;

  try {
    isCreatingOption.value = true;

    // First add to local store for immediate UI feedback
    store.addOption();

    // Prepare option data for API
    const newOptionIndex = store.radioOptions.length - 1;
    const newOption = store.radioOptions[newOptionIndex];

    if (!newOption) {
      throw new Error('Failed to create option in store');
    }

    const optionData: CreateOptionData = {
      optionText: '', // Start with empty string as requested
      itemBlockId: props.itemBlock.id,
      value: newOption.score || 0,
    };

    // Call API to create option
    const createdOption = await optionService.createOption(optionData);

    // Update the local store with backend data and emit to parent
    if (createdOption) {
      // Update the local store with backend data
      if (store.radioOptions[newOptionIndex]) {
        store.radioOptions[newOptionIndex].optionText = createdOption.optionText;
        store.radioOptions[newOptionIndex].score = createdOption.value;
      }

      // Emit the new option to parent component
      emit('option:created', {
        id: createdOption.id,
        itemBlockId: createdOption.itemBlockId,
        optionText: createdOption.optionText,
        value: createdOption.value,
        sequence: createdOption.sequence,
        imagePath: createdOption.imagePath || '',
        nextSection: createdOption.nextSection || 0,
      });
    }

    console.log('✅ Option created successfully:', createdOption);
  } catch (error) {
    console.error('❌ Failed to create option:', error);

    // Remove the option from store if API call failed
    if (store.radioOptions.length > 1) {
      store.removeOption(store.radioOptions.length - 1);
    }

    Notify.create({
      type: 'negative',
      message: 'ไม่สามารถเพิ่มตัวเลือกได้ กรุณาลองใหม่อีกครั้ง',
      position: 'top',
    });
  } finally {
    isCreatingOption.value = false;
  }
};

// Handler for adding "other" option
const handleAddOtherOption = async () => {
  if (isCreatingOption.value) return;

  try {
    isCreatingOption.value = true;

    // First add to local store for immediate UI feedback
    store.addOtherOption();

    // Prepare option data for API
    const newOptionIndex = store.radioOptions.length - 1;
    const newOption = store.radioOptions[newOptionIndex];

    if (!newOption) {
      throw new Error('Failed to create other option in store');
    }

    const optionData: CreateOptionData = {
      optionText: '', // Start with empty string as requested
      itemBlockId: props.itemBlock.id,
      value: newOption.score || 0,
    };

    // Call API to create option
    const createdOption = await optionService.createOption(optionData);

    // Update the local store with backend data and emit to parent
    if (createdOption) {
      // Update the local store with backend data
      if (store.radioOptions[newOptionIndex]) {
        store.radioOptions[newOptionIndex].optionText = createdOption.optionText;
        store.radioOptions[newOptionIndex].score = createdOption.value;
      }

      // Emit the new option to parent component
      emit('option:created', {
        id: createdOption.id,
        itemBlockId: createdOption.itemBlockId,
        optionText: createdOption.optionText,
        value: createdOption.value,
        sequence: createdOption.sequence,
        imagePath: createdOption.imagePath || '',
        nextSection: createdOption.nextSection || 0,
      });
    }

    console.log('✅ Other option created successfully:', createdOption);
  } catch (error) {
    console.error('❌ Failed to create other option:', error);

    // Remove the option from store if API call failed
    if (store.radioOptions.length > 1) {
      store.removeOption(store.radioOptions.length - 1);
    }

    Notify.create({
      type: 'negative',
      message: 'ไม่สามารถเพิ่มตัวเลือก "อื่นๆ" ได้ กรุณาลองใหม่อีกครั้ง',
      position: 'top',
    });
  } finally {
    isCreatingOption.value = false;
  }
};
</script>

<style scoped>
.q-input {
  max-width: 400px;
  width: 400px;
  margin-right: 10px;
}
.draggable-row {
  transition: all 0.3s ease;
}

.draggable-row:hover,
.draggable-row[dragged-index]:hover {
  background-color: #f0f0f0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.draggable-row.dragging {
  opacity: 0.5;
}
</style>
