<template>
  <q-page padding>
    <div class="q-pa-md">
      <h4>Option Service Integration Test</h4>

      <!-- Test Controls -->
      <div class="row q-gutter-md q-mb-lg">
        <q-input
          v-model="testItemBlockId"
          label="Item Block ID"
          type="number"
          outlined
          dense
          style="width: 200px"
        />
        <q-input
          v-model="testQuestionId"
          label="Question ID (optional)"
          type="number"
          outlined
          dense
          style="width: 200px"
        />
        <q-input
          v-model="testOptionText"
          label="Option Text (leave empty to test empty string creation)"
          outlined
          dense
          style="width: 400px"
        />
        <q-input
          v-model="testOptionValue"
          label="Option Value"
          type="number"
          outlined
          dense
          style="width: 150px"
        />
      </div>

      <!-- Action Buttons -->
      <div class="row q-gutter-md q-mb-lg">
        <q-btn
          color="primary"
          label="Create Option (POST /options)"
          @click="testCreateOption"
          :loading="isLoading"
          :disable="!testItemBlockId"
        />
        <q-btn
          color="secondary"
          label="Create Option (Question)"
          @click="testCreateOptionForQuestion"
          :loading="isLoading"
          :disable="!testQuestionId || !testItemBlockId || !testOptionText"
        />
        <q-btn
          color="orange"
          label="Update Option"
          @click="testUpdateOption"
          :loading="isLoading"
          :disable="!lastCreatedOptionId || !testOptionText"
        />
        <q-btn color="negative" label="Clear Results" @click="clearResults" :disable="isLoading" />
      </div>

      <!-- Results Display -->
      <div class="q-mb-lg">
        <h5>Test Results:</h5>
        <q-card v-if="testResults.length > 0" class="q-pa-md">
          <div v-for="(result, index) in testResults" :key="index" class="q-mb-md">
            <div class="text-weight-bold">{{ result.action }}</div>
            <div class="text-caption text-grey-7">{{ result.timestamp }}</div>
            <div v-if="result.success" class="text-positive">✅ Success: {{ result.message }}</div>
            <div v-else class="text-negative">❌ Error: {{ result.message }}</div>
            <div v-if="result.data" class="q-mt-sm">
              <q-expansion-item label="Response Data" dense>
                <pre class="text-caption">{{ JSON.stringify(result.data, null, 2) }}</pre>
              </q-expansion-item>
            </div>
            <q-separator v-if="index < testResults.length - 1" class="q-mt-md" />
          </div>
        </q-card>
        <div v-else class="text-grey-7">No test results yet. Run a test to see results here.</div>
      </div>

      <!-- API Endpoints Info -->
      <div class="q-mb-lg">
        <h5>API Endpoints Being Tested:</h5>
        <q-list bordered>
          <q-item>
            <q-item-section>
              <q-item-label class="text-weight-bold">POST /options</q-item-label>
              <q-item-label caption>Create option with itemBlockId in request body</q-item-label>
            </q-item-section>
          </q-item>
          <q-item>
            <q-item-section>
              <q-item-label class="text-weight-bold">POST /options/{questionId}</q-item-label>
              <q-item-label caption>Create option for specific question</q-item-label>
            </q-item-section>
          </q-item>
          <q-item>
            <q-item-section>
              <q-item-label class="text-weight-bold">PATCH /options/{optionId}</q-item-label>
              <q-item-label caption>Update option by optionId</q-item-label>
            </q-item-section>
          </q-item>
        </q-list>
      </div>

      <!-- Usage Instructions -->
      <div>
        <h5>Usage Instructions:</h5>
        <q-list>
          <q-item>
            <q-item-section avatar>
              <q-icon name="info" color="blue" />
            </q-item-section>
            <q-item-section>
              <q-item-label
                >1. Enter a valid Item Block ID (required for all operations)</q-item-label
              >
            </q-item-section>
          </q-item>
          <q-item>
            <q-item-section avatar>
              <q-icon name="info" color="blue" />
            </q-item-section>
            <q-item-section>
              <q-item-label>2. Enter option text and value</q-item-label>
            </q-item-section>
          </q-item>
          <q-item>
            <q-item-section avatar>
              <q-icon name="info" color="blue" />
            </q-item-section>
            <q-item-section>
              <q-item-label>3. Test creation using POST /options endpoint</q-item-label>
            </q-item-section>
          </q-item>
          <q-item>
            <q-item-section avatar>
              <q-icon name="info" color="blue" />
            </q-item-section>
            <q-item-section>
              <q-item-label>4. After creating an option, test updating it</q-item-label>
            </q-item-section>
          </q-item>
        </q-list>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import {
  OptionService,
  type CreateOptionData,
  type UpdateOptionData,
} from 'src/services/asm/optionService';

// Test data
const testItemBlockId = ref<number>(1);
const testQuestionId = ref<number>();
const testOptionText = ref<string>('Test Option');
const testOptionValue = ref<number>(1);
const lastCreatedOptionId = ref<number>();

// Loading state
const isLoading = ref(false);

// Test results
interface TestResult {
  action: string;
  timestamp: string;
  success: boolean;
  message: string;
  data?: unknown;
}

const testResults = ref<TestResult[]>([]);

// Initialize option service
const optionService = new OptionService();

// Helper function to add test result
const addTestResult = (action: string, success: boolean, message: string, data?: unknown) => {
  testResults.value.unshift({
    action,
    timestamp: new Date().toLocaleString(),
    success,
    message,
    data,
  });
};

// Test create option using POST /options
const testCreateOption = async () => {
  if (!testItemBlockId.value) return;

  isLoading.value = true;
  try {
    const optionData: CreateOptionData = {
      optionText: testOptionText.value || '', // Allow empty string for testing
      itemBlockId: testItemBlockId.value,
      value: testOptionValue.value || 1,
    };

    const result = await optionService.createOption(optionData);

    lastCreatedOptionId.value = result.id;
    addTestResult(
      'Create Option (POST /options)',
      true,
      `Option created with ID: ${result.id}, optionText: "${result.optionText}"`,
      result,
    );
  } catch (error) {
    addTestResult(
      'Create Option (POST /options)',
      false,
      error instanceof Error ? error.message : 'Unknown error',
      error,
    );
  } finally {
    isLoading.value = false;
  }
};

// Test create option for question
const testCreateOptionForQuestion = async () => {
  if (!testQuestionId.value || !testItemBlockId.value || !testOptionText.value) return;

  isLoading.value = true;
  try {
    const optionData: CreateOptionData = {
      optionText: testOptionText.value,
      itemBlockId: testItemBlockId.value,
      value: testOptionValue.value || 1,
    };

    const result = await optionService.createOptionForQuestion(testQuestionId.value, optionData);

    lastCreatedOptionId.value = result.id;
    addTestResult(
      'Create Option for Question',
      true,
      `Option created with ID: ${result.id}`,
      result,
    );
  } catch (error) {
    addTestResult(
      'Create Option for Question',
      false,
      error instanceof Error ? error.message : 'Unknown error',
      error,
    );
  } finally {
    isLoading.value = false;
  }
};

// Test update option
const testUpdateOption = async () => {
  if (!lastCreatedOptionId.value || !testOptionText.value) return;

  isLoading.value = true;
  try {
    const updateData: UpdateOptionData = {
      optionText: `${testOptionText.value} (Updated)`,
      itemBlockId: testItemBlockId.value,
      value: testOptionValue.value || 1,
    };

    const result = await optionService.updateOption(lastCreatedOptionId.value, updateData);

    addTestResult(
      'Update Option',
      true,
      `Option ${lastCreatedOptionId.value} updated successfully`,
      result,
    );
  } catch (error) {
    addTestResult(
      'Update Option',
      false,
      error instanceof Error ? error.message : 'Unknown error',
      error,
    );
  } finally {
    isLoading.value = false;
  }
};

// Clear test results
const clearResults = () => {
  testResults.value = [];
  lastCreatedOptionId.value = undefined;
};
</script>

<style scoped>
pre {
  background-color: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
  overflow-x: auto;
}
</style>
